{"kind": "singleType", "collectionName": "globals", "info": {"singularName": "global", "pluralName": "globals", "displayName": "Global", "description": "Define global settings"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"siteName": {"type": "string", "required": true}, "favicon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos"]}, "defaultSeo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "logo2": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "map": {"type": "text"}}}